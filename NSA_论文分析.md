# Native Sparse Attention (NSA) 论文深度分析

## 论文信息

- **标题**: Native Sparse Attention: Hardware-Aligned and Natively Trainable Sparse Attention
- **作者**: Jingyang Yuan, Huazuo Gao, Damai Dai 等
- **机构**: DeepSeek-AI, 北京大学, 华盛顿大学
- **论文编号**: 2502.11089v2

---

## 摘要 (Abstract) 分析

### 问题定义与背景

长文本建模对下一代语言模型至关重要，但标准注意力机制的高计算成本带来了显著的计算挑战。稀疏注意力为在保持模型能力的同时提高效率提供了一个有前途的方向。

### 解决方案提出

论文提出了**NSA (Native Sparse Attention)**，一个将算法创新与硬件对齐优化相结合的原生可训练稀疏注意力机制，用于实现高效的长文本建模。

### 核心价值主张

NSA的核心定位由两个关键特性定义：

1. **原生可训练 (Natively trainable)**: 稀疏性从一开始就融入模型架构，让模型天生具备稀疏处理能力
2. **硬件对齐 (Hardware-aligned)**: 深入到GPU等现代硬件的执行层面，确保理论加速转化为实际速度提升

### 技术架构

NSA采用**动态分层稀疏策略**，结合：

- **粗粒度令牌压缩**: 负责全局上下文感知
- **细粒度令牌选择**: 负责保留局部精度

### 关键创新

1. **硬件对齐系统**: 通过平衡算术强度的算法设计实现显著加速
2. **端到端训练**: 在不牺牲模型性能的情况下减少预训练计算

### 实验结果

- 在通用基准测试、长文本任务和指令推理中保持或超越全注意力模型
- 在64k长度序列上，在解码、前向传播和反向传播中都实现了显著加速

---

## 1. 引言 (Introduction) 分析

### 第一层次：定义问题与动机

**市场需求**: 长文本处理是下一代大模型的核心战场，包括深度推理、代码生成、多轮对话等应用场景。

**性能瓶颈**: 标准注意力机制的 $O(n^2)$ 复杂度使其成为关键瓶颈。在64k长度序列解码时，注意力计算占总延迟的70-80%。

**技术路线**: 选择稀疏注意力作为主要解决方案，在效率和能力之间取得平衡。

### 第二层次：现有方案的局限性

**理论与实际的差距**: 许多稀疏注意力方法在理论上减少了计算量，但在实际部署中未能实现相应的延迟降低。

**架构兼容性问题**: 现有方法难以适配现代高效架构如GQA和MQA，导致内存访问效率低下。

### 第三层次：设计原则确立

确立了两个核心设计原则：

1. **硬件对齐的推理加速**: 将理论计算减少转化为实际速度提升
2. **训练感知的算法设计**: 支持端到端训练以减少训练成本

### 第四层次：解决方案预览

NSA通过三个并行注意力分支处理不同类型的信息：

- **压缩分支**: 处理粗粒度模式
- **选择分支**: 处理重要令牌块
- **滑动窗口分支**: 处理局部上下文

---

## 2. 重新思考稀疏注意力方法 (Rethinking Sparse Attention Methods)

### 2.1. 高效推理的幻觉 (The Illusion of Efficient Inference)

#### 阶段性稀疏 (Phase-Restricted Sparsity)

**问题描述**: 许多方法只优化推理生命周期的某一阶段

- **H2O**: 优化解码阶段，但预填充阶段需要密集计算
- **MInference**: 只优化预填充阶段，解码效率未提升

#### 与先进架构的不兼容性

**GQA/MQA架构下的问题**:

- 在MHA下，每个头独立选择KV子集，内存访问量确实减少
- 在GQA下，需要加载所有头选择的KV块的并集，内存访问量可能不减反增

### 2.2. 可训练稀疏的神话 (The Myth of Trainable Sparsity)

#### 性能退化

后置稀疏强制模型偏离预训练轨迹。研究表明，top-20%的注意力只能覆盖70%的总注意力分数。

#### 现有方法的训练挑战

- **非可训练组件**: 如k-means聚类、SimHash选择等离散操作阻断梯度流
- **低效反向传播**: 令牌级选择导致非连续内存访问，无法利用FlashAttention优化

---

## 3. 方法论 (Methodology) 详解

本章是论文的技术核心，详细阐述NSA的算法设计和内核优化。

### 3.1. 背景 (Background)

#### 注意力机制

标准注意力机制的数学表达：

$$
\mathbf{o}_t = \text{Attn}(\mathbf{q}_t, \mathbf{k}_{:t}, \mathbf{v}_{:t})
$$

其中注意力函数定义为：

$$
\text{Attn}(\mathbf{q}_t, \mathbf{k}_{:t}, \mathbf{v}_{:t}) = \sum_{i=1}^{t} \frac{\alpha_{t,i} \mathbf{v}_i}{\sum_{j=1}^{t} \alpha_{t,j}}
$$

$$
\alpha_{t,i} = \exp\left(\frac{\mathbf{q}_t^T \mathbf{k}_i}{\sqrt{d_k}}\right)
$$

#### 算术强度 (Arithmetic Intensity)

**定义**: 计算操作次数与内存访问次数的比值

**硬件特性**:

- **计算密集型**: 算术强度高，瓶颈在GPU FLOPS
- **内存密集型**: 算术强度低，瓶颈在内存带宽

**LLM阶段特性**:

- **训练/预填充**: 计算密集型，优化目标是减少计算量
- **自回归解码**: 内存密集型，优化目标是减少内存访问量

### 3.2. 整体框架 (Overall Framework)

#### 核心思想

将完整的K/V对替换为更紧凑、信息密集的表示，为每个查询动态构建。

#### 数学表达

**动态K/V构建**:

$$
\tilde{K}_t = f_K(\mathbf{q}_t, \mathbf{k}_{:t}, \mathbf{v}_{:t})
$$

$$
\tilde{V}_t = f_V(\mathbf{q}_t, \mathbf{k}_{:t}, \mathbf{v}_{:t})
$$

$$
\mathbf{o}_t^* = \text{Attn}(\mathbf{q}_t, \tilde{K}_t, \tilde{V}_t)
$$

**多策略门控融合**:

$$
\mathbf{o}_t^* = \sum_{c \in C} g_t^c \cdot \text{Attn}(\mathbf{q}_t, \tilde{K}_t^c, \tilde{V}_t^c)
$$

其中：

- $C = \{\text{cmp}, \text{slc}, \text{win}\}$: 压缩、选择、滑动窗口三种策略
- $g_t^c \in [0,1]$: 可学习的门控分数，由MLP和Sigmoid激活生成

**稀疏性约束**:

$$
N_t = \sum_{c \in C} \text{size}[\tilde{K}_t^c] \ll t
$$

### 3.3. 算法设计 (Algorithm Design)

#### 3.3.1. 令牌压缩 (Token Compression)

**目标**: 通过聚合连续K/V块捕获粗粒度全局信息

**数学定义**:

$$
\tilde{K}_t^{\text{cmp}} = f_K^{\text{cmp}}(\mathbf{k}_{:t}) = \left\{\varphi(\mathbf{k}_{id+1:id+l}) \mid 0 \leq i \leq \left\lfloor\frac{t-l}{d}\right\rfloor\right\}
$$

其中：

- $l$: 块长度
- $d$: 相邻块间的滑动步长
- $\varphi$: 可学习的MLP，将块内令牌映射为单个压缩表示

**工程考量**:

- 通常设置 $d < l$ 以减少信息碎片化
- 压缩表示 $\tilde{K}_t^{\text{cmp}} \in \mathbb{R}^{d_k \times \lfloor\frac{t-l}{d}\rfloor}$

#### 3.3.2. 令牌选择 (Token Selection)

**动机**: 压缩可能丢失重要的细粒度信息，需要选择性保留关键令牌

##### 块级选择策略

**硬件效率考虑**: 现代GPU对连续块访问的吞吐量远高于随机索引读取

**注意力分布特性**: 注意力分数通常表现出空间连续性，相邻键往往具有相似的重要性水平

##### 重要性分数计算

**核心创新**: 复用压缩分支的中间计算结果，避免额外开销

**压缩分支的注意力分数**:

$$
\mathbf{p}_t^{\text{cmp}} = \text{Softmax}(\mathbf{q}_t^T \tilde{K}_t^{\text{cmp}})
$$

**选择块重要性分数**:

当压缩块和选择块使用相同分块方案时（$l' = l = d$）:

$$
\mathbf{p}_t^{\text{slc}} = \mathbf{p}_t^{\text{cmp}}
$$

对于不同分块方案，通过空间关系推导：

$$
\mathbf{p}_t^{\text{slc}}[j] = \sum_{m=0}^{\frac{l'}{d}-1} \sum_{n=0}^{\frac{l}{d}-1} \mathbf{p}_t^{\text{cmp}}\left[\frac{l'}{d}j - m - n\right]
$$

##### GQA兼容性

**问题**: 在GQA架构下，同组内查询头必须选择一致的块以最小化KV缓存加载

**解决方案**: 组内重要性分数聚合

$$
\mathbf{p}_t^{\text{slc}'} = \sum_{h=1}^{H} \mathbf{p}_t^{\text{slc},(h)}
$$

其中 $H$ 是同一组内的查询头数量。

##### Top-K块选择

$$
\mathcal{I}_t = \{i \mid \text{rank}(\mathbf{p}_t^{\text{slc}'}[i]) \leq n\}
$$

$$
\tilde{K}_t^{\text{slc}} = \text{Cat}\left[\{\mathbf{k}_{il'+1:(i+1)l'} \mid i \in \mathcal{I}_t\}\right]
$$

#### 3.3.3. 滑动窗口 (Sliding Window)

**设计动机**: 防止局部模式主导学习过程，确保其他分支能够专注于各自特征

**实现方式**:

$$
\tilde{K}_t^{\text{win}} = \mathbf{k}_{t-w:t}, \quad \tilde{V}_t^{\text{win}} = \mathbf{v}_{t-w:t}
$$

**架构隔离**: 为三个分支提供独立的K/V，防止梯度干扰

### 3.4. 内核设计 (Kernel Design)

#### 核心挑战

传统FlashAttention按时间顺序加载连续查询块，但在稀疏选择下会导致KV块的非连续访问。

#### NSA的解决方案

**查询分组策略**: 按GQA组加载查询，而非按时间顺序

**关键特性**:

1. **组中心数据加载**: 每个内循环加载GQA组内所有查询头 $Q \in \mathbb{R}^{[h, d_k]}$ 和共享的稀疏KV块索引 $\mathcal{I}_t$
2. **共享KV获取**: 顺序加载索引为 $\mathcal{I}_t$ 的连续KV块到SRAM: $K \in \mathbb{R}^{[B_k, d_k]}, V \in \mathbb{R}^{[B_k, d_v]}$
3. **网格外循环**: 由于内循环长度（正比于选择块数 $n$）对不同查询块几乎相同，将查询/输出循环放在Triton网格调度器中

**算术强度优化**:

- 通过组级共享消除冗余KV传输
- 在GPU流多处理器间平衡计算工作负载

---

## 4. 实验 (Experiments) 分析

### 4.1. 预训练设置 (Pretraining Setup)

**模型配置**:

- **参数规模**: 27B总参数，3B激活参数
- **架构**: GQA + MoE结合
- **层数**: 30层，隐藏维度2560
- **GQA配置**: 4组，总共64个注意力头
- **MoE配置**: 72个路由专家 + 2个共享专家，top-6选择

**NSA超参数**:

- 压缩块大小 $l = 32$
- 滑动步长 $d = 16$
- 选择块大小 $l' = 64$
- 选择块数量 $n = 16$
- 滑动窗口大小 $w = 512$

**训练数据**: 270B令牌，8k长度文本预训练，后续32k长度文本持续训练

### 4.2. 基线方法 (Baseline Methods)

**对比方法**:

- **H2O**: KV缓存驱逐方法
- **InfLLM**: 查询感知选择
- **Quest**: 块级选择策略
- **Exact-Top**: 精确top-k稀疏选择

**评估设置**: 所有稀疏方法设置相同稀疏度（2560个激活令牌）以确保公平比较

### 4.3. 性能对比 (Performance Comparison)

#### 通用评估

**评估基准**: MMLU, MMLU-PRO, CMMLU, BBH, GSM8K, MATH, DROP, MBPP, HumanEval

**关键结果**:

- NSA在9个指标中的7个超越Full Attention
- 在推理相关基准上显著提升（DROP: +0.042, GSM8K: +0.034）
- 平均性能0.456 vs Full Attention的0.443

#### 长文本评估

**Needle-in-a-Haystack测试**: NSA在64k上下文的所有位置实现完美检索准确率

**LongBench结果**:

- 平均分0.469，超越Full Attention (+0.032)和Exact-Top (+0.046)
- 在多跳QA任务上表现突出（HPQ: +0.087, 2Wiki: +0.051）
- 代码理解任务显著提升（LCC: +0.069）

#### 思维链推理评估

**实验设置**: 使用DeepSeek-R1知识蒸馏，10B令牌32k长度数学推理轨迹SFT

**AIME 24结果**:

- 8k上下文: NSA-R 0.121 vs Full Attention-R 0.046 (+0.075)
- 16k上下文: NSA-R 0.146 vs Full Attention-R 0.092 (+0.054)

---

## 5. 效率分析 (Efficiency Analysis)

### 5.1. 训练速度 (Training Speed)

**硬件环境**: 8-GPU A100系统，Triton后端实现

**加速比结果**:

- 8k长度: 前向传播加速，反向传播加速
- 16k长度: 更显著的加速比
- 32k长度: 持续增长的优势
- 64k长度: 前向9.0×，反向6.0×加速

**加速来源**:

- 块级内存访问模式最大化Tensor Core利用率
- 内核中的精细循环调度消除冗余KV传输

### 5.2. 解码速度 (Decoding Speed)

**内存访问分析**:

每个解码步骤的内存访问量：

| 上下文长度 | Full Attention | NSA  | 预期加速比 |
| ---------- | -------------- | ---- | ---------- |
| 8192       | 8192           | 2048 | 4.0×      |
| 16384      | 16384          | 2560 | 6.4×      |
| 32768      | 32768          | 3584 | 9.1×      |
| 65536      | 65536          | 5632 | 11.6×     |

**加速公式**:

NSA每步需要加载：

- 压缩令牌: $\lfloor\frac{s-l}{d}\rfloor$
- 选择令牌: $nl'$
- 邻近令牌: $w$

总计: $\lfloor\frac{s-l}{d}\rfloor + nl' + w$ 个令牌

---

## 6. 讨论 (Discussion)

### 6.1. 替代令牌选择策略的挑战

#### 基于聚类的策略

**ClusterKV等方法的问题**:

- 动态聚类机制引入非平凡计算开销
- 集群间不平衡加剧MoE系统中的负载不平衡
- 需要定期重聚类和块顺序训练协议

#### 其他块级选择策略

**Quest和InfLLM等方法的局限**:

- 非可微选择操作依赖辅助损失，增加算子开销
- 启发式无参数重要性分数计算召回率低
- 冷启动训练方法仍表现出较差的损失曲线

### 6.2. 可视化分析 (Visualization)

**注意力图模式**: 全注意力模型的可视化显示注意力分数呈现块级聚类特征，相邻键通常显示相似的注意力分数。

**设计启发**: 这一观察激发了NSA基于空间连续性选择键块的设计，表明在连续令牌块上操作的稀疏注意力机制可能是一个有前途的方向。

---

## 7. 相关工作 (Related Works)

### 7.1. 固定稀疏模式 (Fixed Sparse Pattern)

**代表方法**:

- **SlidingWindow**: 固定窗口内计算注意力
- **StreamingLLM**: 注意力汇聚 + 局部窗口
- **Longformer**: 局部窗口 + 全局令牌交错

**与NSA的区别**: NSA不依赖预定义模式，而是自动学习稀疏模式

### 7.2. 动态令牌剪枝 (Dynamic Token Pruning)

**代表方法**:

- **H2O, BUZZ, SepLLM**: 自适应KV缓存剪枝
- **FastGen, HeadKV**: 头级别不同策略
- **SnapKV**: 选择性特征保留

**局限性**: 主要针对推理优化，训练阶段支持有限

### 7.3. 查询感知选择 (Query-Aware Selection)

**代表方法**:

- **Quest**: 块级重要性估计
- **InfLLM**: 固定模式 + 检索结合
- **HashAttention**: 汉明空间映射
- **ClusterKV**: 聚类后选择
- **SeerAttention**: 空间块级选择

**NSA优势**: 在完整模型生命周期（训练、预填充、解码）实现硬件对齐的稀疏注意力计算

---

## 8. 结论 (Conclusion)

### 核心贡献

NSA通过将分层令牌压缩与块级令牌选择集成在可训练架构中，实现了硬件对齐的稀疏注意力架构，用于高效长文本建模。

### 技术成就

1. **性能保持**: 在通用基准上匹配全注意力基线性能
2. **长文本优势**: 在长文本评估中超越建模能力
3. **推理增强**: 展现增强的推理能力
4. **效率提升**: 实现可测量的计算延迟减少和显著加速

### 未来方向

NSA为稀疏注意力的实用化部署提供了新的范式，其原生可训练和硬件对齐的设计理念为未来高效长文本模型的发展奠定了基础。随着模型规模和上下文长度的持续增长，类似NSA这样的硬件对齐、原生可训练的稀疏方法将成为必然选择。
